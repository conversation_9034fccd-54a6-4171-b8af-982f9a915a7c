using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Newtonsoft.Json;
using System.Text;
using PBC.AggregatorService.DTOs;

namespace PBC.AggregatorService.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class HelpdeskController : ControllerBase
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<HelpdeskController> _logger;
        private readonly IConfiguration _configuration;

        public HelpdeskController(HttpClient httpClient, ILogger<HelpdeskController> logger, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _logger = logger;
            _configuration = configuration;
        }

        #region ::: SelectDealerName :::
        /// <summary>
        /// Select dealer name
        /// </summary>
        /// <param name="request">Dealer name selection request</param>
        /// <returns>Dealer name selection result</returns>
        [HttpPost("select-dealer-name")]
        [Authorize]
        public async Task<IActionResult> SelectDealerName([FromBody] SelectDealerNameList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/select-dealer-name");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Create request with configuration
                var requestWithConfig = new
                {
                    request = request,
                    connectionString = connString
                };

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/select-dealer-name";

                var jsonContent = JsonConvert.SerializeObject(requestWithConfig.request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelectDealerName");
                return StatusCode(500, "An error occurred while processing dealer name selection");
            }
        }
        #endregion

        #region ::: InitialSetup :::
        /// <summary>
        /// Get initial setup data
        /// </summary>
        /// <param name="request">Initial setup request</param>
        /// <returns>Initial setup data</returns>
        [HttpPost("initial-setup")]
        [Authorize]
        public async Task<IActionResult> InitialSetup([FromBody] InitialSetupList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/initial-setup");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/initial-setup";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in InitialSetup");
                return StatusCode(500, "An error occurred while processing initial setup");
            }
        }
        #endregion

        #region ::: GetObjectID :::
        /// <summary>
        /// Get object ID by name
        /// </summary>
        /// <param name="name">Object name</param>
        /// <returns>Object ID</returns>
        [HttpGet("object-id/{name}")]
        [Authorize]
        public async Task<IActionResult> GetObjectID(string name)
        {
            try
            {
                _logger.LogInformation("GET /api/Helpdesk/object-id/{Name}", name);

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/object-id/{name}";

                var response = await _httpClient.GetAsync($"{endpoint}?connectionString={connString}");
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetObjectID for name: {Name}", name);
                return StatusCode(500, "An error occurred while getting object ID");
            }
        }
        #endregion

        #region ::: GetBranchName :::
        /// <summary>
        /// Get branch name by ID
        /// </summary>
        /// <param name="branchId">Branch ID</param>
        /// <returns>Branch name</returns>
        [HttpGet("branch-name/{branchId}")]
        [Authorize]
        public async Task<IActionResult> GetBranchName(int branchId)
        {
            try
            {
                _logger.LogInformation("GET /api/Helpdesk/branch-name/{BranchId}", branchId);

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/branch-name/{branchId}";

                var response = await _httpClient.GetAsync($"{endpoint}?connectionString={connString}");
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetBranchName for ID: {BranchId}", branchId);
                return StatusCode(500, "An error occurred while getting branch name");
            }
        }
        #endregion

        #region ::: GetTabsData :::
        /// <summary>
        /// Get tabs data by company ID
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <returns>Tabs data</returns>
        [HttpGet("tabs-data/{companyId}")]
        [Authorize]
        public async Task<IActionResult> GetTabsData(int companyId)
        {
            try
            {
                _logger.LogInformation("GET /api/Helpdesk/tabs-data/{CompanyId}", companyId);

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/tabs-data/{companyId}";

                var response = await _httpClient.GetAsync($"{endpoint}?connectionString={connString}");
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetTabsData for company ID: {CompanyId}", companyId);
                return StatusCode(500, "An error occurred while getting tabs data");
            }
        }
        #endregion

        #region ::: CheckAddPermissions :::
        /// <summary>
        /// Check add permissions for user
        /// </summary>
        /// <param name="request">Permission check request</param>
        /// <returns>Permission check result</returns>
        [HttpPost("check-add-permissions")]
        [Authorize]
        public async Task<IActionResult> CheckAddPermissions([FromBody] CheckAddPermissionsRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/check-add-permissions");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/check-add-permissions";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CheckAddPermissions");
                return StatusCode(500, "An error occurred while checking permissions");
            }
        }
        #endregion

        #region ::: ValidateCalldateAndPCD :::
        /// <summary>
        /// Validate call date and PCD
        /// </summary>
        /// <param name="request">Date validation request</param>
        /// <returns>Validation result</returns>
        [HttpPost("validate-calldate-pcd")]
        [Authorize]
        public async Task<IActionResult> ValidateCalldateAndPCD([FromBody] validateCalldateandPCDList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/validate-calldate-pcd");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/validate-calldate-pcd";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ValidateCalldateAndPCD");
                return StatusCode(500, "An error occurred while validating dates");
            }
        }
        #endregion

        #region ::: CheckBayWorkshopAvailability :::
        /// <summary>
        /// Check bay workshop availability
        /// </summary>
        /// <param name="request">Workshop availability check request</param>
        /// <returns>Availability check result</returns>
        [HttpPost("check-bay-workshop-availability")]
        [Authorize]
        public async Task<IActionResult> CheckBayWorkshopAvailability([FromBody] CheckBayWorkshopAvailabilityList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/check-bay-workshop-availability");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/check-bay-workshop-availability";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CheckBayWorkshopAvailability");
                return StatusCode(500, "An error occurred while checking workshop availability");
            }
        }
        #endregion

        #region ::: CheckForWorkshopBlockOverlap :::
        /// <summary>
        /// Check for workshop block overlap
        /// </summary>
        /// <param name="request">Workshop block overlap check request</param>
        /// <returns>Overlap check result</returns>
        [HttpPost("check-workshop-block-overlap")]
        [Authorize]
        public async Task<IActionResult> CheckForWorkshopBlockOverlap([FromBody] CheckForWorkshopBlockOverlapList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/check-workshop-block-overlap");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/check-workshop-block-overlap";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CheckForWorkshopBlockOverlap");
                return StatusCode(500, "An error occurred while checking workshop block overlap");
            }
        }
        #endregion

        #region ::: SelectFieldSearchParty :::
        /// <summary>
        /// Select field search party
        /// </summary>
        /// <param name="request">Field search party request</param>
        /// <returns>Field search party result</returns>
        [HttpPost("select-field-search-party")]
        [Authorize]
        public async Task<IActionResult> SelectFieldSearchParty([FromBody] SelectFieldSearchPartyRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/select-field-search-party");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/select-field-search-party";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelectFieldSearchParty");
                return StatusCode(500, "An error occurred while searching parties");
            }
        }
        #endregion

        #region ::: GetCustomerData :::
        /// <summary>
        /// Get customer data
        /// </summary>
        /// <param name="request">Customer data request</param>
        /// <returns>Customer data</returns>
        [HttpPost("get-customer-data")]
        [Authorize]
        public async Task<IActionResult> GetCustomerData([FromBody] GetCustomerDataRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-customer-data");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/get-customer-data";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetCustomerData");
                return StatusCode(500, "An error occurred while getting customer data");
            }
        }
        #endregion

        #region ::: GetDealerData :::
        /// <summary>
        /// Get dealer data
        /// </summary>
        /// <param name="request">Dealer data request</param>
        /// <returns>Dealer data</returns>
        [HttpPost("get-dealer-data")]
        [Authorize]
        public async Task<IActionResult> GetDealerData([FromBody] GetDealerDataRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-dealer-data");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/get-dealer-data";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetDealerData");
                return StatusCode(500, "An error occurred while getting dealer data");
            }
        }
        #endregion

        #region ::: GetProductDetails :::
        /// <summary>
        /// Get product details
        /// </summary>
        /// <param name="request">Product details request</param>
        /// <returns>Product details</returns>
        [HttpPost("get-product-details")]
        [Authorize]
        public async Task<IActionResult> GetProductDetails([FromBody] GetProductDetailsRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-product-details");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/get-product-details";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetProductDetails");
                return StatusCode(500, "An error occurred while getting product details");
            }
        }
        #endregion

        #region ::: CheckDuplicateContactPerson :::
        /// <summary>
        /// Check duplicate contact person
        /// </summary>
        /// <param name="request">Duplicate contact person check request</param>
        /// <returns>Duplicate check result</returns>
        [HttpPost("check-duplicate-contact-person")]
        [Authorize]
        public async Task<IActionResult> CheckDuplicateContactPerson([FromBody] CheckDuplicateContactPersonRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/check-duplicate-contact-person");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/check-duplicate-contact-person";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CheckDuplicateContactPerson");
                return StatusCode(500, "An error occurred while checking duplicate contact person");
            }
        }
        #endregion

        #region ::: GetOpenCampaignDetails :::
        /// <summary>
        /// Get open campaign details
        /// </summary>
        /// <param name="request">Campaign details request</param>
        /// <returns>Campaign details</returns>
        [HttpPost("get-open-campaign-details")]
        [Authorize]
        public async Task<IActionResult> GetOpenCampaignDetails([FromBody] GetOpenCampaignDetailsRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-open-campaign-details");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/get-open-campaign-details";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetOpenCampaignDetails");
                return StatusCode(500, "An error occurred while getting campaign details");
            }
        }
        #endregion

        #region ::: GetInitialData :::
        /// <summary>
        /// Get initial data for help desk user landing page
        /// </summary>
        /// <param name="request">Initial data request</param>
        /// <returns>Initial data</returns>
        [HttpPost("get-initial-data")]
        [Authorize]
        public async Task<IActionResult> GetInitialData([FromBody] GetInitialDataList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-initial-data");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/get-initial-data";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetInitialData");
                return StatusCode(500, "An error occurred while getting initial data");
            }
        }
        #endregion

        #region ::: SelectServiceRequest :::
        /// <summary>
        /// Select service request records with pagination and filtering
        /// </summary>
        /// <param name="request">Service request selection request</param>
        /// <returns>Service request records</returns>
        [HttpPost("select-service-request")]
        [Authorize]
        public async Task<IActionResult> SelectServiceRequest([FromBody] SelectServiceRequestRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/select-service-request");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/select-service-request";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelectServiceRequest");
                return StatusCode(500, "An error occurred while selecting service requests");
            }
        }
        #endregion

        #region ::: GetTabDetails :::
        /// <summary>
        /// Get tab details for a specific tab
        /// </summary>
        /// <param name="id">Tab ID</param>
        /// <param name="param">Parameter name</param>
        /// <param name="companyId">Company ID</param>
        /// <returns>Tab details</returns>
        [HttpGet("get-tab-details")]
        [Authorize]
        public async Task<IActionResult> GetTabDetails([FromQuery] int id, [FromQuery] string param, [FromQuery] int companyId)
        {
            try
            {
                _logger.LogInformation("GET /api/Helpdesk/get-tab-details");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/get-tab-details?id={id}&param={param}&companyId={companyId}";

                var response = await _httpClient.GetAsync($"{endpoint}&connectionString={connString}");
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetTabDetails");
                return StatusCode(500, "An error occurred while getting tab details");
            }
        }
        #endregion

        #region ::: SelWorkFlowSummary :::
        /// <summary>
        /// Select workflow summary data
        /// </summary>
        /// <param name="request">Workflow summary request</param>
        /// <returns>Workflow summary data</returns>
        [HttpPost("select-workflow-summary")]
        [Authorize]
        public async Task<IActionResult> SelWorkFlowSummary([FromBody] GetWorkFlowSummaryList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/select-workflow-summary");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/select-workflow-summary";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelWorkFlowSummary");
                return StatusCode(500, "An error occurred while getting workflow summary");
            }
        }
        #endregion

        #region ::: SaveCustomer :::
        /// <summary>
        /// Save customer details
        /// </summary>
        /// <param name="request">Customer save request</param>
        /// <returns>Save result</returns>
        [HttpPost("save-customer")]
        [Authorize]
        public async Task<IActionResult> SaveCustomer([FromBody] SaveCustomerList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/save-customer");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/save-customer";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SaveCustomer");
                return StatusCode(500, "An error occurred while saving customer");
            }
        }
        #endregion

        #region ::: UpdateIsEditTicket :::
        /// <summary>
        /// Update edit ticket status
        /// </summary>
        /// <param name="request">Update edit ticket request</param>
        /// <returns>Update result</returns>
        [HttpPost("update-is-edit-ticket")]
        [Authorize]
        public async Task<IActionResult> UpdateIsEditTicket([FromBody] UpdateIsEditTicketList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/update-is-edit-ticket");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/update-is-edit-ticket";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in UpdateIsEditTicket");
                return StatusCode(500, "An error occurred while updating edit ticket status");
            }
        }
        #endregion

        #region ::: HelpDeskServiceRequest Endpoints :::

        #region ::: LoadMasters :::
        /// <summary>
        /// Load Masters
        /// </summary>
        /// <param name="request">Load masters request</param>
        /// <returns>Masters data</returns>
        [HttpPost("load-masters")]
        [Authorize]
        public async Task<IActionResult> LoadMasters([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/load-masters");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/load-masters";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in LoadMasters");
                return StatusCode(500, "An error occurred while loading masters");
            }
        }
        #endregion

        #region ::: LoadIssueSubArea :::
        /// <summary>
        /// Load Issue Sub Area
        /// </summary>
        /// <param name="request">Load issue sub area request</param>
        /// <returns>Issue sub area data</returns>
        [HttpPost("load-issue-sub-area")]
        [Authorize]
        public async Task<IActionResult> LoadIssueSubArea([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/load-issue-sub-area");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/load-issue-sub-area";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in LoadIssueSubArea");
                return StatusCode(500, "An error occurred while loading issue sub area");
            }
        }
        #endregion

        #region ::: GetPartyDetailsbyID :::
        /// <summary>
        /// Get Party Details by ID
        /// </summary>
        /// <param name="request">Get party details request</param>
        /// <returns>Party details</returns>
        [HttpPost("get-party-details-by-id")]
        [Authorize]
        public async Task<IActionResult> GetPartyDetailsbyID([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-party-details-by-id");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/get-party-details-by-id";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetPartyDetailsbyID");
                return StatusCode(500, "An error occurred while getting party details");
            }
        }
        #endregion

        #region ::: SelectPartyDetailGrid :::
        /// <summary>
        /// Select Party Detail Grid
        /// </summary>
        /// <param name="request">Select party detail grid request</param>
        /// <returns>Party detail grid data</returns>
        [HttpPost("select-party-detail-grid")]
        [Authorize]
        public async Task<IActionResult> SelectPartyDetailGrid([FromBody] object request, [FromQuery] string sidx = "", [FromQuery] string sord = "", [FromQuery] int page = 1, [FromQuery] int rows = 10)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/select-party-detail-grid");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/select-party-detail-grid";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}&sidx={sidx}&sord={sord}&page={page}&rows={rows}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelectPartyDetailGrid");
                return StatusCode(500, "An error occurred while selecting party detail grid");
            }
        }
        #endregion

        #region ::: GetProductUniqueNumber :::
        /// <summary>
        /// Get Product Unique Number
        /// </summary>
        /// <param name="request">Get product unique number request</param>
        /// <returns>Product unique number</returns>
        [HttpPost("get-product-unique-number")]
        [Authorize]
        public async Task<IActionResult> GetProductUniqueNumber([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-product-unique-number");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/get-product-unique-number";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetProductUniqueNumber");
                return StatusCode(500, "An error occurred while getting product unique number");
            }
        }
        #endregion

        #region ::: GetProductWarranty :::
        /// <summary>
        /// Get Product Warranty
        /// </summary>
        /// <param name="request">Get product warranty request</param>
        /// <returns>Product warranty data</returns>
        [HttpPost("get-product-warranty")]
        [Authorize]
        public async Task<IActionResult> GetProductWarranty([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-product-warranty");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/get-product-warranty";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetProductWarranty");
                return StatusCode(500, "An error occurred while getting product warranty");
            }
        }
        #endregion

        #region ::: ValidateReading :::
        /// <summary>
        /// Validate Reading
        /// </summary>
        /// <param name="request">Validate reading request</param>
        /// <returns>Validation result</returns>
        [HttpPost("validate-reading")]
        [Authorize]
        public async Task<IActionResult> ValidateReading([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/validate-reading");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/validate-reading";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ValidateReading");
                return StatusCode(500, "An error occurred while validating reading");
            }
        }
        #endregion

        #region ::: ValidateSerialNumber :::
        /// <summary>
        /// Validate Serial Number
        /// </summary>
        /// <param name="request">Validate serial number request</param>
        /// <returns>Validation result</returns>
        [HttpPost("validate-serial-number")]
        [Authorize]
        public async Task<IActionResult> ValidateSerialNumber([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/validate-serial-number");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/validate-serial-number";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ValidateSerialNumber");
                return StatusCode(500, "An error occurred while validating serial number");
            }
        }
        #endregion

        #region ::: GetBrandProductType :::
        /// <summary>
        /// Get Brand Product Type
        /// </summary>
        /// <param name="request">Get brand product type request</param>
        /// <returns>Brand product type data</returns>
        [HttpPost("get-brand-product-type")]
        [Authorize]
        public async Task<IActionResult> GetBrandProductType([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-brand-product-type");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/get-brand-product-type";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetBrandProductType");
                return StatusCode(500, "An error occurred while getting brand product type");
            }
        }
        #endregion

        #region ::: GetSerialNumberForModelforDealer :::
        /// <summary>
        /// Get Serial Number For Model for Dealer
        /// </summary>
        /// <param name="request">Get serial number for model for dealer request</param>
        /// <returns>Serial number data</returns>
        [HttpPost("get-serial-number-for-model-for-dealer")]
        [Authorize]
        public async Task<IActionResult> GetSerialNumberForModelforDealer([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-serial-number-for-model-for-dealer");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/get-serial-number-for-model-for-dealer";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetSerialNumberForModelforDealer");
                return StatusCode(500, "An error occurred while getting serial number for model for dealer");
            }
        }
        #endregion

        #region ::: GetCustomerDetailsByPhone :::
        /// <summary>
        /// Get Customer Details by Phone
        /// </summary>
        /// <param name="request">Get customer details by phone request</param>
        /// <returns>Customer details</returns>
        [HttpPost("get-customer-details-by-phone")]
        [Authorize]
        public async Task<IActionResult> GetCustomerDetailsByPhone([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-customer-details-by-phone");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/get-customer-details-by-phone";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetCustomerDetailsByPhone");
                return StatusCode(500, "An error occurred while getting customer details by phone");
            }
        }
        #endregion

        #region ::: GetCustomerDetailsByEmail :::
        /// <summary>
        /// Get Customer Details by Email
        /// </summary>
        /// <param name="request">Get customer details by email request</param>
        /// <returns>Customer details</returns>
        [HttpPost("get-customer-details-by-email")]
        [Authorize]
        public async Task<IActionResult> GetCustomerDetailsByEmail([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-customer-details-by-email");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/get-customer-details-by-email";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetCustomerDetailsByEmail");
                return StatusCode(500, "An error occurred while getting customer details by email");
            }
        }
        #endregion

        #region ::: ContactPersonMasterSave :::
        /// <summary>
        /// Contact Person Master Save
        /// </summary>
        /// <param name="request">Contact person master save request</param>
        /// <returns>Save result</returns>
        [HttpPost("contact-person-master-save")]
        [Authorize]
        public async Task<IActionResult> ContactPersonMasterSave([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/contact-person-master-save");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/contact-person-master-save";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ContactPersonMasterSave");
                return StatusCode(500, "An error occurred while saving contact person master");
            }
        }
        #endregion

        #region ::: GetAllProductDetailsForDealer :::
        /// <summary>
        /// Get All Product Details For Dealer
        /// </summary>
        /// <param name="request">Get all product details for dealer request</param>
        /// <returns>Product details</returns>
        [HttpPost("get-all-product-details-for-dealer")]
        [Authorize]
        public async Task<IActionResult> GetAllProductDetailsForDealer([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-all-product-details-for-dealer");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/get-all-product-details-for-dealer";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetAllProductDetailsForDealer");
                return StatusCode(500, "An error occurred while getting all product details for dealer");
            }
        }
        #endregion

        #region ::: GetRolesForActions :::
        /// <summary>
        /// Get Roles For Actions
        /// </summary>
        /// <param name="request">Get roles for actions request</param>
        /// <returns>Roles data</returns>
        [HttpPost("get-roles-for-actions")]
        [Authorize]
        public async Task<IActionResult> GetRolesForActions([FromBody] object request, [FromQuery] string helpDesk = "")
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-roles-for-actions");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/get-roles-for-actions";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}&helpDesk={helpDesk}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetRolesForActions");
                return StatusCode(500, "An error occurred while getting roles for actions");
            }
        }
        #endregion

        #region ::: GetMovementofWorkFlow :::
        /// <summary>
        /// Get Movement of Work Flow
        /// </summary>
        /// <param name="request">Get movement of work flow request</param>
        /// <returns>Work flow movement data</returns>
        [HttpPost("get-movement-of-work-flow")]
        [Authorize]
        public async Task<IActionResult> GetMovementofWorkFlow([FromBody] object request, [FromQuery] string helpDesk = "", [FromQuery] string sidx = "", [FromQuery] string sord = "", [FromQuery] int page = 1, [FromQuery] int rows = 10)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-movement-of-work-flow");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/get-movement-of-work-flow";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}&helpDesk={helpDesk}&sidx={sidx}&sord={sord}&page={page}&rows={rows}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetMovementofWorkFlow");
                return StatusCode(500, "An error occurred while getting movement of work flow");
            }
        }
        #endregion

        #region ::: Insert :::
        /// <summary>
        /// Insert Service Request
        /// </summary>
        /// <param name="request">Insert service request</param>
        /// <returns>Insert result</returns>
        [HttpPost("insert")]
        [Authorize]
        public async Task<IActionResult> Insert([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/insert");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/insert";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Insert");
                return StatusCode(500, "An error occurred while inserting service request");
            }
        }
        #endregion

        #region ::: Edit :::
        /// <summary>
        /// Edit Service Request
        /// </summary>
        /// <param name="request">Edit service request</param>
        /// <returns>Edit result</returns>
        [HttpPost("edit")]
        [Authorize]
        public async Task<IActionResult> Edit([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/edit");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/edit";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Edit");
                return StatusCode(500, "An error occurred while editing service request");
            }
        }
        #endregion

        #region ::: GetMovementofWorkFlowforAll :::
        /// <summary>
        /// Get Movement of Work Flow for All
        /// </summary>
        /// <param name="request">Get movement of work flow for all request</param>
        /// <returns>Work flow movement data for all</returns>
        [HttpPost("get-movement-of-work-flow-for-all")]
        [Authorize]
        public async Task<IActionResult> GetMovementofWorkFlowforAll([FromBody] object request, [FromQuery] string helpDesk = "", [FromQuery] string sidx = "", [FromQuery] string sord = "", [FromQuery] int page = 1, [FromQuery] int rows = 10)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-movement-of-work-flow-for-all");
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/get-movement-of-work-flow-for-all";
                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}&helpDesk={helpDesk}&sidx={sidx}&sord={sord}&page={page}&rows={rows}", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetMovementofWorkFlowforAll");
                return StatusCode(500, "An error occurred while getting movement of work flow for all");
            }
        }
        #endregion

        #region ::: CheckSRSequence :::
        /// <summary>
        /// Check SR Sequence
        /// </summary>
        /// <param name="request">Check SR sequence request</param>
        /// <returns>SR sequence check result</returns>
        [HttpPost("check-sr-sequence")]
        [Authorize]
        public async Task<IActionResult> CheckSRSequence([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/check-sr-sequence");
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/check-sr-sequence";
                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CheckSRSequence");
                return StatusCode(500, "An error occurred while checking SR sequence");
            }
        }
        #endregion

        #region ::: SelHDProductDetails :::
        /// <summary>
        /// Select HD Product Details
        /// </summary>
        /// <param name="request">Select HD product details request</param>
        /// <returns>HD product details</returns>
        [HttpPost("sel-hd-product-details")]
        [Authorize]
        public async Task<IActionResult> SelHDProductDetails([FromBody] object request, [FromQuery] string sidx = "", [FromQuery] string sord = "", [FromQuery] int page = 1, [FromQuery] int rows = 10, [FromQuery] bool _search = false, [FromQuery] string filters = "")
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/sel-hd-product-details");
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/sel-hd-product-details";
                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}&sidx={sidx}&sord={sord}&page={page}&rows={rows}&_search={_search}&filters={filters}", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelHDProductDetails");
                return StatusCode(500, "An error occurred while selecting HD product details");
            }
        }
        #endregion

        #region ::: GetScheduledDropins :::
        /// <summary>
        /// Get Scheduled Dropins
        /// </summary>
        /// <param name="request">Get scheduled dropins request</param>
        /// <returns>Scheduled dropins data</returns>
        [HttpPost("get-scheduled-dropins")]
        [Authorize]
        public async Task<IActionResult> GetScheduledDropins([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-scheduled-dropins");
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/get-scheduled-dropins";
                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetScheduledDropins");
                return StatusCode(500, "An error occurred while getting scheduled dropins");
            }
        }
        #endregion

        #region ::: SelectAllDropdownData :::
        /// <summary>
        /// Select All Dropdown Data
        /// </summary>
        /// <param name="request">Select all dropdown data request</param>
        /// <returns>Dropdown data</returns>
        [HttpPost("select-all-dropdown-data")]
        [Authorize]
        public async Task<IActionResult> SelectAllDropdownData([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/select-all-dropdown-data");
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/select-all-dropdown-data";
                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelectAllDropdownData");
                return StatusCode(500, "An error occurred while selecting all dropdown data");
            }
        }
        #endregion

        #region ::: SelectFieldSearchName :::
        /// <summary>
        /// Select Field Search Name
        /// </summary>
        /// <param name="request">Select field search name request</param>
        /// <returns>Field search name data</returns>
        [HttpPost("select-field-search-name")]
        [Authorize]
        public async Task<IActionResult> SelectFieldSearchName([FromBody] object request, [FromQuery] string sidx = "", [FromQuery] string sord = "", [FromQuery] int page = 1, [FromQuery] int rows = 10, [FromQuery] bool _search = false, [FromQuery] string filters = "", [FromQuery] bool advnce = false, [FromQuery] string Query = "")
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/select-field-search-name");
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/select-field-search-name";
                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}&sidx={sidx}&sord={sord}&page={page}&rows={rows}&_search={_search}&filters={filters}&advnce={advnce}&Query={Query}", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelectFieldSearchName");
                return StatusCode(500, "An error occurred while selecting field search name");
            }
        }
        #endregion

        #endregion

        #region ::: InsertSR :::
        /// <summary>
        /// Insert Service Request
        /// </summary>
        /// <param name="request">Service request data</param>
        /// <returns>Insert result</returns>
        [HttpPost("insert-sr")]
        [Authorize]
        public async Task<IActionResult> InsertSR([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/insert-sr");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/insert-sr";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in InsertSR");
                return StatusCode(500, "An error occurred while inserting service request");
            }
        }
        #endregion

        #region ::: InitialMode :::
        /// <summary>
        /// Get initial mode data
        /// </summary>
        /// <param name="request">Initial mode request</param>
        /// <returns>Initial mode data</returns>
        [HttpPost("initial-mode")]
        [Authorize]
        public async Task<IActionResult> InitialMode([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/initial-mode");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/initial-mode";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in InitialMode");
                return StatusCode(500, "An error occurred while getting initial mode data");
            }
        }
        #endregion

        #region ::: SelectSRDetails :::
        /// <summary>
        /// Select Service Request Details
        /// </summary>
        /// <param name="request">SR details request</param>
        /// <returns>SR details</returns>
        [HttpPost("select-sr-details")]
        [Authorize]
        public async Task<IActionResult> SelectSRDetails([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/select-sr-details");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/select-sr-details";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelectSRDetails");
                return StatusCode(500, "An error occurred while selecting service request details");
            }
        }
        #endregion
        #region ::: GetProductDetailsServiceRequest :::
        /// <summary>
        /// Get Product Details for Service Request
        /// </summary>
        /// <param name="request">Product details request</param>
        /// <returns>Product details</returns>
        [HttpPost("get-product-details-sr")]
        [Authorize]
        public async Task<IActionResult> GetProductDetailsServiceRequest([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-product-details-sr");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/get-product-details";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetProductDetailsServiceRequest");
                return StatusCode(500, "An error occurred while getting product details");
            }
        }
        #endregion

        #region ::: GetActionsServiceRequest :::
        /// <summary>
        /// Get Actions for Service Request
        /// </summary>
        /// <param name="request">Actions request</param>
        /// <returns>Actions data</returns>
        [HttpPost("get-actions-sr")]
        [Authorize]
        public async Task<IActionResult> GetActionsServiceRequest([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-actions-sr");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/get-actions";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetActionsServiceRequest");
                return StatusCode(500, "An error occurred while getting actions");
            }
        }
        #endregion

        #region ::: EditSR :::
        /// <summary>
        /// Edit Service Request
        /// </summary>
        /// <param name="request">Edit SR request</param>
        /// <returns>Edit result</returns>
        [HttpPost("edit-sr")]
        [Authorize]
        public async Task<IActionResult> EditSR([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/edit-sr");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/edit-sr";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in EditSR");
                return StatusCode(500, "An error occurred while editing service request");
            }
        }
        #endregion
        #region ::: GetSRDetails :::
        /// <summary>
        /// Get Service Request Details
        /// </summary>
        /// <param name="request">SR details request</param>
        /// <returns>SR details</returns>
        [HttpPost("get-sr-details")]
        [Authorize]
        public async Task<IActionResult> GetSRDetails([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-sr-details");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/get-sr-details";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetSRDetails");
                return StatusCode(500, "An error occurred while getting service request details");
            }
        }
        #endregion

        #region ::: GetDetailsServiceRequest :::
        /// <summary>
        /// Get Details for Service Request
        /// </summary>
        /// <param name="request">Get details request</param>
        /// <returns>Date details</returns>
        [HttpPost("get-details-sr")]
        [Authorize]
        public async Task<IActionResult> GetDetailsServiceRequest([FromBody] object request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-details-sr");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskservicerequest/get-details";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}&logException={logException}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetDetailsServiceRequest");
                return StatusCode(500, "An error occurred while getting details");
            }
        }
        #endregion
    }
}