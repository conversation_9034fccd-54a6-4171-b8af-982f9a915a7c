using System.Reflection;
using PBC.HelpdeskService.Services;
using PBC.HelpdeskService.Utilities;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = "PBC Helpdesk Service",
        Version = "v1",
        Description = "Contains all Helpdesk functions endpoints"
    });
    c.CustomSchemaIds(type => type.FullName);

    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    c.IncludeXmlComments(xmlPath);
});

// Add HTTP client for inter-service communication
builder.Services.AddHttpClient();

// Add health checks
builder.Services.AddHealthChecks();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Register application services
builder.Services.AddScoped<IHealthService, HealthService>();
builder.Services.AddScoped<IUtilityServiceClient, UtilityServiceClient>();
builder.Services.AddScoped<ICoreServiceClient, CoreServiceClient>();
builder.Services.AddScoped<IWorkflowServiceClient, WorkflowServiceClient>();
builder.Services.AddScoped<IHelpDeskUserLandingPageServices, HelpDeskUserLandingPageServices>();
builder.Services.AddScoped<IHelpDeskServiceRequestServices, HelpDeskServiceRequestServices>();
builder.Services.AddScoped<IHelpdeskServiceResourceHelper, HelpdeskServiceResourceHelper>();


var app = builder.Build();

// Configure the HTTP request pipeline.
// if (app.Environment.IsDevelopment())
// {
//     app.UseSwagger();
//     app.UseSwaggerUI(c =>
//     {
//         c.SwaggerEndpoint("/swagger/v1/swagger.json", "PBC Helpdesk Service V1");
//         c.RoutePrefix = "swagger";
//     });
// }

app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "PBC Helpdesk Service V1");
    c.RoutePrefix = "swagger";
});

app.UseHttpsRedirection();
app.UseCors("AllowAll");
app.UseAuthorization();

app.MapControllers();
app.MapHealthChecks("/health");

app.Run();
