services:
  PBC.AggregatorService:
    build:
      context: ./PBC.AggregatorService/
      dockerfile: Parts/Dockerfile
    image: pbc.aggregatorservice
    ports:
      - 5285:5285
    networks:
      - app-network

  authentication-service:
    build:
      context: ./
      dockerfile: Authentication/Dockerfile
    image: auth-app
    env_file:
      - Authentication/.env.docker
    ports:
      - 5213:5213
    networks:
      - app-network

  aggregator-service:
    build:
      context: ./
      dockerfile: Aggregator/Dockerfile
    image: aggregator-app
    env_file:
      - Aggregator/.env.docker
    ports:
      - 5170:5170
    networks:
      - app-network
    depends_on:
      - parts-service
      - authentication-service

  telemetry-service:
    build:
      context: ./
      dockerfile: Telemetry/Dockerfile
    image: telemetry-app
    env_file:
      - Telemetry/.env.docker
    ports:
      - 3000:3000
    networks:
      - app-network
    depends_on:
      - aggregator-service

  gateway-service:
    build:
      context: ./
      dockerfile: Gateway/Dockerfile
    image: gateway-app
    env_file:
      - Gateway/.env.docker
    ports:
      - 8000:80
    networks:
      - app-network
    depends_on:
      - telemetry-service

networks:
  app-network:
    driver: bridge
