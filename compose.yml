version: '3.8'

services:
  # PBC Utility Service - Shared utilities and common functions
  pbc-utility-service:
    build:
      context: ./PBC.UtilityService
      dockerfile: Dockerfile
    image: pbc.utilityservice:latest
    container_name: pbc-utility-service
    ports:
      - "5003:5003"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:5003
    networks:
      - pbc-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5003/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PBC Core Service - Core business logic
  pbc-core-service:
    build:
      context: ./PBC.CoreService
      dockerfile: Dockerfile
    image: pbc.coreservice:latest
    container_name: pbc-core-service
    ports:
      - "5001:5001"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:5001
    networks:
      - pbc-network
    depends_on:
      - pbc-utility-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PBC Helpdesk Service - Helpdesk operations
  pbc-helpdesk-service:
    build:
      context: ./PBC.HelpdeskService
      dockerfile: Dockerfile
    image: pbc.helpdeskservice:latest
    container_name: pbc-helpdesk-service
    ports:
      - "5002:5002"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:5002
    networks:
      - pbc-network
    depends_on:
      - pbc-utility-service
      - pbc-core-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PBC Workflow Service - Workflow management
  pbc-workflow-service:
    build:
      context: ./PBC.WorkflowService
      dockerfile: Dockerfile
    image: pbc.workflowservice:latest
    container_name: pbc-workflow-service
    ports:
      - "5005:5005"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:5005
    networks:
      - pbc-network
    depends_on:
      - pbc-utility-service
      - pbc-core-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5005/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PBC Aggregator Service - API Gateway and aggregation
  pbc-aggregator-service:
    build:
      context: ./PBC.AggregatorService
      dockerfile: Dockerfile
    image: pbc.aggregatorservice:latest
    container_name: pbc-aggregator-service
    ports:
      - "5004:5004"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:5004
      - ServiceUrls__CoreService=http://pbc-core-service:5001
      - ServiceUrls__UtilitiesService=http://pbc-utility-service:5003
      - ServiceUrls__HelpdeskService=http://pbc-helpdesk-service:5002
      - ServiceUrls__WorkflowService=http://pbc-workflow-service:5005
    networks:
      - pbc-network
    depends_on:
      - pbc-utility-service
      - pbc-core-service
      - pbc-helpdesk-service
      - pbc-workflow-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5004/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  pbc-network:
    driver: bridge
    name: pbc-microservices-network
