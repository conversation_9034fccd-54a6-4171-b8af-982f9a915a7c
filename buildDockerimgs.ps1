# Enable strict mode
Set-StrictMode -Version Latest

Write-Host "Building Docker images for PBC Microservices..." -ForegroundColor Green
Write-Host ""

# Define all PBC services with their paths and Docker image tags
$services = @(
    @{ Path = "PBC.UtilityService";    Tag = "pbc.utilityservice";    Port = "5003" },
    @{ Path = "PBC.CoreService";       Tag = "pbc.coreservice";       Port = "5001" },
    @{ Path = "PBC.HelpdeskService";   Tag = "pbc.helpdeskservice";   Port = "5002" },
    @{ Path = "PBC.AggregatorService"; Tag = "pbc.aggregatorservice"; Port = "5004" },
    @{ Path = "PBC.WorkflowService";   Tag = "pbc.workflowservice";   Port = "5005" }
)

$totalServices = $services.Count
$currentService = 0

foreach ($svc in $services) {
    $currentService++
    $servicePath = $svc.Path
    $imageTag    = $svc.Tag
    $servicePort = $svc.Port

    Write-Host "[$currentService/$totalServices] Building $servicePath (Port: $servicePort)..." -ForegroundColor Yellow

    # Resolve the folder
    try {
        $fullServicePath = (Resolve-Path $servicePath -ErrorAction Stop).Path
    } catch {
        Write-Host "⚠️  Service folder not found: $servicePath" -ForegroundColor DarkYellow
        continue
    }

    $dockerfilePath = Join-Path $fullServicePath "Dockerfile"
    if (-Not (Test-Path $dockerfilePath)) {
        Write-Host "⚠️  No Dockerfile found in $fullServicePath" -ForegroundColor DarkYellow
        continue
    }

    Write-Host "   📁 Service path: $fullServicePath" -ForegroundColor DarkGray
    Write-Host "   🐳 Dockerfile: $dockerfilePath" -ForegroundColor DarkGray
    Write-Host "   🏷️  Image tag: $imageTag:latest" -ForegroundColor DarkGray

    # Build arguments for Docker
    $buildArgs = @(
        "build",
        "--file",  $dockerfilePath,
        "--tag",   "$imageTag:latest",
        "--build-arg", "SERVICE_PORT=$servicePort",
        $fullServicePath
    )

    # Show exactly what will run
    Write-Host "   🔨 Command: docker $($buildArgs -join ' ')" -ForegroundColor DarkGray
    Write-Host ""

    # Execute Docker build
    $buildStartTime = Get-Date
    & docker @buildArgs

    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌  Failed to build $imageTag" -ForegroundColor Red
        Write-Host "   Exit code: $LASTEXITCODE" -ForegroundColor Red
        exit $LASTEXITCODE
    } else {
        $buildEndTime = Get-Date
        $buildDuration = ($buildEndTime - $buildStartTime).TotalSeconds
        Write-Host "✅  $imageTag built successfully in $([math]::Round($buildDuration, 1)) seconds" -ForegroundColor Green
    }

    Write-Host ""
}

Write-Host "🎉 All Docker build tasks completed successfully!" -ForegroundColor Cyan
Write-Host ""
Write-Host "Built images:" -ForegroundColor Green
foreach ($svc in $services) {
    Write-Host "  • $($svc.Tag):latest (Port: $($svc.Port))" -ForegroundColor Cyan
}
Write-Host ""
Write-Host "To run the services with Docker Compose, use:" -ForegroundColor Yellow
Write-Host "  docker-compose up -d" -ForegroundColor White
Write-Host ""
Write-Host "To view running containers:" -ForegroundColor Yellow
Write-Host "  docker ps" -ForegroundColor White
Write-Host ""

exit 0
