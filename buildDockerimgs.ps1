# Enable strict mode
Set-StrictMode -Version Latest

Write-Host "Building Docker images for PBC Microservices..." -ForegroundColor Green
Write-Host ""

$services = @(
    @{ Path = "PBC.UtilityService";    Tag = "pbc.utilityservice" },
    @{ Path = "PBC.CoreService";       Tag = "pbc.coreservice" },
    @{ Path = "PBC.HelpdeskService";   Tag = "pbc.helpdeskservice" },
    @{ Path = "PBC.AggregatorService"; Tag = "pbc.aggregatorservice" },
    @{ Path = "PBC.WorkflowService";   Tag = "pbc.workflowservice" }
)

foreach ($svc in $services) {
    $servicePath = $svc.Path
    $imageTag    = $svc.Tag

    Write-Host "→ Building $servicePath..." -ForegroundColor Yellow

    # Resolve the folder
    try {
        $fullServicePath = (Resolve-Path $servicePath -ErrorAction Stop).Path
    } catch {
        Write-Host "⚠️  Service folder not found: $servicePath" -ForegroundColor DarkYellow
        continue
    }

    $dockerfilePath = Join-Path $fullServicePath "Dockerfile"
    if (-Not (Test-Path $dockerfilePath)) {
        Write-Host "⚠️  No Dockerfile in $fullServicePath" -ForegroundColor DarkYellow
        continue
    }

    # --- HERE'S THE CRUCIAL BIT: each element MUST be its own array item ---
    $buildArgs = @(
        "build",
        "--file",  $dockerfilePath,
        "--tag",   "$imageTag:latest",
        $fullServicePath
    )

    # Show exactly what will run:
    Write-Host "   & docker $($buildArgs -join ' ')" -ForegroundColor DarkGray

    # Now invoke Docker properly:
    & docker @buildArgs

    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌  Failed to build $imageTag" -ForegroundColor Red
        exit $LASTEXITCODE
    } else {
        Write-Host "✅  $imageTag built successfully." -ForegroundColor Green
    }

    Write-Host ""
}

Write-Host "🎉 All Docker build tasks completed." -ForegroundColor Cyan
exit 0
